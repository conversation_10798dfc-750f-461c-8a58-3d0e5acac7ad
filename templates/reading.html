{% extends "base.html" %}

{% block title %}LearnFast - Tananyag Olvasás{% endblock %}

{% block content %}
<div class="reading-container">
    <div class="reading-sidebar">
        <h3>Fejezetek</h3>
        <div class="chapter-tree" id="chapter-tree">
            <!-- This will be populated by JavaScript -->
        </div>
    </div>

    <div class="reading-main">
        <div class="reading-header">
            <h2>Tananyag Olvasás</h2>
            <div class="progress-info">
                <span id="current-chapter-title">{{ current_content.chapter if current_content else 'Nincs fejezet' }}</span>
                <span class="chapter-counter">(<span id="current-index">1</span> / <span id="total-chapters">{{ total_chapters }}</span>)</span>
            </div>
        </div>

        <div class="reading-content" id="reading-content">
            {% if current_content %}
            <div class="content-section">
                <h3>{{ current_content.chapter }} fejezet</h3>
                
                <div class="content-text" id="content-en">
                    {{ current_content.content_en }}
                </div>
                
                <div class="content-text-hu" id="content-hu" style="display: none;">
                    {{ current_content.content_hu }}
                </div>
                
                <div class="content-actions">
                    <button class="btn btn-info" id="show-hungarian">Mutasd magyarul</button>
                </div>
            </div>
            {% else %}
            <div class="no-content">
                <h3>Nincs elérhető tananyag</h3>
                <p>A kiválasztott fejezetekhez nem található tananyag.</p>
            </div>
            {% endif %}
        </div>

        <div class="reading-navigation">
            <button class="btn btn-secondary" id="prev-chapter" {% if not has_prev %}disabled{% endif %}>
                ← Előző fejezet
            </button>
            <button class="btn btn-primary" id="next-chapter" {% if not has_next %}disabled{% endif %}>
                Következő fejezet →
            </button>
        </div>

        <div class="reading-controls">
            <a href="{{ url_for('index') }}" class="btn btn-secondary">Vissza a főoldalra</a>
            <button class="btn btn-warning" id="restart-reading">Újrakezdés</button>
        </div>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" id="contents-data">
    {{ contents_json | safe }}
</script>
<script type="application/json" id="chapters-data">
    {{ chapters_json | safe }}
</script>
{% endblock %}

{% block scripts %}
<script>




document.addEventListener('DOMContentLoaded', function() {
    const contentsData = JSON.parse(document.getElementById('contents-data').textContent);
    const chaptersData = JSON.parse(document.getElementById('chapters-data').textContent);

    let currentContentIndex = 0;
    
    // Elements
    const contentEn = document.getElementById('content-en');
    const contentHu = document.getElementById('content-hu');
    const showHungarianBtn = document.getElementById('show-hungarian');
    const prevChapterBtn = document.getElementById('prev-chapter');
    const nextChapterBtn = document.getElementById('next-chapter');
    const restartBtn = document.getElementById('restart-reading');
    const currentChapterTitle = document.getElementById('current-chapter-title');
    const currentIndexSpan = document.getElementById('current-index');
    const chapterTree = document.getElementById('chapter-tree');
    
    function buildChapterTree() {
        const mainChapters = chaptersData.filter(c => c.is_main_chapter);
        const subChapters = chaptersData.filter(c => !c.is_main_chapter);
        
        let treeHtml = '<ul class="chapter-list">';
        
        mainChapters.forEach(mainChapter => {
            treeHtml += `<li class="main-chapter-item">`;
            treeHtml += `<span class="main-chapter-title">${mainChapter.id} - ${mainChapter.name}</span>`;
            
            const relatedSubs = subChapters.filter(sub => sub.id.startsWith(mainChapter.id + '.'));
            if (relatedSubs.length > 0) {
                treeHtml += '<ul class="sub-chapter-list">';
                relatedSubs.forEach(subChapter => {
                    const hasContent = contentsData.some(c => c.chapter === subChapter.id);
                    const activeClass = contentsData[currentContentIndex] && contentsData[currentContentIndex].chapter === subChapter.id ? 'active' : '';
                    treeHtml += `<li class="sub-chapter-item ${activeClass}" data-chapter="${subChapter.id}">`;
                    treeHtml += `<span class="sub-chapter-title ${hasContent ? 'has-content' : 'no-content'}">${subChapter.id} - ${subChapter.name}</span>`;
                    treeHtml += '</li>';
                });
                treeHtml += '</ul>';
            }
            treeHtml += '</li>';
        });
        
        treeHtml += '</ul>';
        chapterTree.innerHTML = treeHtml;
        
        // Add click handlers
        document.querySelectorAll('.sub-chapter-item').forEach(item => {
            item.addEventListener('click', function() {
                const chapterId = this.dataset.chapter;
                const contentIndex = contentsData.findIndex(c => c.chapter === chapterId);
                if (contentIndex !== -1) {
                    currentContentIndex = contentIndex;
                    loadContent(currentContentIndex);
                    updateChapterTree();
                }
            });
        });
    }
    
    function updateChapterTree() {
        document.querySelectorAll('.sub-chapter-item').forEach(item => {
            item.classList.remove('active');
            if (contentsData[currentContentIndex] && item.dataset.chapter === contentsData[currentContentIndex].chapter) {
                item.classList.add('active');
            }
        });
    }
    
    function loadContent(index) {
        if (index < 0 || index >= contentsData.length) return;
        
        const content = contentsData[index];
        
        // Reset visibility
        contentHu.style.display = 'none';
        showHungarianBtn.textContent = 'Mutasd magyarul';
        
        // Load content
        contentEn.innerHTML = content.content_en;
        contentHu.innerHTML = content.content_hu;
        currentChapterTitle.textContent = content.chapter;
        currentIndexSpan.textContent = index + 1;
        
        // Update navigation buttons
        prevChapterBtn.disabled = index === 0;
        nextChapterBtn.disabled = index === contentsData.length - 1;
        
        // Update tree
        updateChapterTree();
    }
    
    // Event listeners
    showHungarianBtn.addEventListener('click', function() {
        contentHu.style.display = contentHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = contentHu.style.display === 'none' ? 'Mutasd magyarul' : 'Rejtsd el a magyart';
    });
    
    prevChapterBtn.addEventListener('click', function() {
        if (currentContentIndex > 0) {
            currentContentIndex--;
            loadContent(currentContentIndex);
        }
    });
    
    nextChapterBtn.addEventListener('click', function() {
        if (currentContentIndex < contentsData.length - 1) {
            currentContentIndex++;
            loadContent(currentContentIndex);
        }
    });
    
    restartBtn.addEventListener('click', function() {
        currentContentIndex = 0;
        loadContent(currentContentIndex);
    });
    
    // Initialize
    buildChapterTree();
    if (contentsData.length > 0) {
        loadContent(currentContentIndex);
    }
});
</script>
{% endblock %}
