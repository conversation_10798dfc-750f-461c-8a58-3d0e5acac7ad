{% extends "base.html" %}

{% block title %}LearnFast - Learning Cards{% endblock %}

{% block content %}
<div class="container">
    <div class="learning-header">
        <h2>Learning Cards</h2>
        <div class="progress-info">
            <span id="current-card">1</span> / <span id="total-cards">{{ total_cards }}</span>
        </div>
    </div>

    <div class="learning-card" id="learning-card">
        <div class="card-content">
            <div class="question-section">
                <h3>Kérdés:</h3>
                <div class="question-text" id="question-en">
                    {{ current_card.question }}
                </div>
                <div class="question-text-hu" id="question-hu" style="display: none;">
                    {{ current_card.question_hu }}
                </div>
                <button class="btn btn-info" id="show-question-hu">Mutasd magyarul</button>
            </div>

            <div class="answer-section" id="answer-section" style="display: none;">
                <h3>Válasz:</h3>
                <div class="answer-text" id="answer-en">
                    {{ current_card.answer }}
                </div>
                <div class="answer-text-hu" id="answer-hu" style="display: none;">
                    {{ current_card.answer_hu }}
                </div>
                <button class="btn btn-info" id="show-answer-hu" style="display: none;">Mutasd a választ magyarul</button>
            </div>

            <div class="chapter-content-section" id="chapter-content-section" style="display: none;">
                <h3>Tananyag - {{ current_card.chapter }}:</h3>
                <div class="content-text" id="content-en">
                    {{ chapter_content.content_en | safe if chapter_content else 'Nincs elérhető tananyag ehhez a fejezethez.' }}
                </div>
                <div class="content-text-hu" id="content-hu" style="display: none;">
                    {{ chapter_content.content_hu | safe if chapter_content else 'Nincs elérhető magyar tananyag ehhez a fejezethez.' }}
                </div>
                <button class="btn btn-info" id="show-content-hu" style="display: none;">Mutasd a tananyagot magyarul</button>
            </div>
        </div>

        <div class="card-actions">
            <button class="btn btn-secondary" id="show-answer">Mutasd a választ</button>
            <button class="btn btn-info" id="show-content">Mutasd a tananyagot</button>
            <button class="btn btn-primary" id="next-card">Következő kérdés</button>
        </div>
    </div>

    <div class="learning-controls">
        <a href="{{ url_for('index') }}" class="btn btn-secondary">Vissza a főoldalra</a>
        <button class="btn btn-warning" id="restart-learning">Újrakezdés</button>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" id="cards-data">
    {{ cards_json | safe }}
</script>
<script type="application/json" id="contents-data">
    {{ contents_json | safe }}
</script>
{% endblock %}

{% block scripts %}
<script>




document.addEventListener('DOMContentLoaded', function() {
    const cardsData = JSON.parse(document.getElementById('cards-data').textContent);
    const contentsData = JSON.parse(document.getElementById('contents-data').textContent);

    let currentCardIndex = 0;
    
    // Elements
    const questionEn = document.getElementById('question-en');
    const questionHu = document.getElementById('question-hu');
    const answerEn = document.getElementById('answer-en');
    const answerHu = document.getElementById('answer-hu');
    const contentEn = document.getElementById('content-en');
    const contentHu = document.getElementById('content-hu');
    
    const showQuestionHuBtn = document.getElementById('show-question-hu');
    const showAnswerBtn = document.getElementById('show-answer');
    const showAnswerHuBtn = document.getElementById('show-answer-hu');
    const showContentBtn = document.getElementById('show-content');
    const showContentHuBtn = document.getElementById('show-content-hu');
    const nextCardBtn = document.getElementById('next-card');
    const restartBtn = document.getElementById('restart-learning');
    
    const answerSection = document.getElementById('answer-section');
    const contentSection = document.getElementById('chapter-content-section');
    const currentCardSpan = document.getElementById('current-card');
    
    function loadCard(index) {
        const card = cardsData[index];
        const content = contentsData.find(c => c.chapter === card.chapter);
        
        // Reset visibility
        questionHu.style.display = 'none';
        answerSection.style.display = 'none';
        answerHu.style.display = 'none';
        contentSection.style.display = 'none';
        contentHu.style.display = 'none';
        showAnswerHuBtn.style.display = 'none';
        showContentHuBtn.style.display = 'none';
        
        // Load content
        questionEn.textContent = card.question;
        questionHu.textContent = card.question_hu;
        answerEn.textContent = card.answer;
        answerHu.textContent = card.answer_hu;

        if (content) {
            contentEn.innerHTML = content.content_en;
            contentHu.innerHTML = content.content_hu;
        } else {
            contentEn.textContent = 'Nincs elérhető tananyag ehhez a fejezethez.';
            contentHu.textContent = 'Nincs elérhető magyar tananyag ehhez a fejezethez.';
        }
        
        currentCardSpan.textContent = index + 1;
    }
    
    // Event listeners
    showQuestionHuBtn.addEventListener('click', function() {
        questionHu.style.display = questionHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = questionHu.style.display === 'none' ? 'Mutasd magyarul' : 'Rejtsd el a magyart';
    });
    
    showAnswerBtn.addEventListener('click', function() {
        answerSection.style.display = 'block';
        showAnswerHuBtn.style.display = 'inline-block';
    });
    
    showAnswerHuBtn.addEventListener('click', function() {
        answerHu.style.display = answerHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = answerHu.style.display === 'none' ? 'Mutasd a választ magyarul' : 'Rejtsd el a magyar választ';
    });
    
    showContentBtn.addEventListener('click', function() {
        contentSection.style.display = 'block';
        showContentHuBtn.style.display = 'inline-block';
    });
    
    showContentHuBtn.addEventListener('click', function() {
        contentHu.style.display = contentHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = contentHu.style.display === 'none' ? 'Mutasd a tananyagot magyarul' : 'Rejtsd el a magyar tananyagot';
    });
    
    nextCardBtn.addEventListener('click', function() {
        currentCardIndex = (currentCardIndex + 1) % cardsData.length;
        loadCard(currentCardIndex);
    });
    
    restartBtn.addEventListener('click', function() {
        currentCardIndex = 0;
        loadCard(currentCardIndex);
    });

    // Initialize with first card
    loadCard(currentCardIndex);
});
</script>
{% endblock %}
