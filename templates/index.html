{% extends "base.html" %}

{% block title %}LearnFast - Főoldal{% endblock %}

{% block content %}
<div class="container">
    <h2>Válassz tananyagot</h2>
    
    <div class="subjects-grid">
        {% for subject in subjects %}
        <div class="subject-card">
            <h3>{{ subject }}</h3>
            <form method="POST" action="{{ url_for('select_subject') }}">
                <input type="hidden" name="subject" value="{{ subject }}">
                <button type="submit" class="btn btn-primary">Kiv<PERSON>lasztás</button>
            </form>
        </div>
        {% endfor %}
    </div>

    {% if session.get('current_subject') %}
    <div class="study-options">
        <h2>Tanulási módok - {{ session.get('current_subject') }}</h2>
        
        <div class="mode-selection">
            <div class="mode-card">
                <h3>Learning Cards</h3>
                <p>Tanulókártyák segítségével sajátítsd el az anyagot</p>
                <button class="btn btn-success" onclick="startLearningMode()">Indítás</button>
            </div>

            <div class="mode-card">
                <h3>Practice Quiz</h3>
                <p>Teszteld a tudásod kvíz kérdésekkel</p>
                <button class="btn btn-warning" onclick="startPracticeMode()">Indítás</button>
            </div>

            <div class="mode-card">
                <h3>Tananyag Olvasás</h3>
                <p>Olvass végig a tananyagon fejezetek szerint</p>
                <button class="btn btn-info" onclick="startReadingMode()">Indítás</button>
            </div>

            <div class="mode-card">
                <h3>Big Random</h3>
                <p>Minden kérdés összekeverve - teljes random tanulás</p>
                <button class="btn btn-danger" onclick="startBigRandomMode()">Indítás</button>
            </div>
        </div>

        <div class="chapter-selection">
            <h3>Fejezetek kiválasztása</h3>
            <form id="chapter-form">
                <div class="chapters-grid">
                    {% for chapter in chapters %}
                    <div class="chapter-item">
                        <label>
                            <input type="checkbox" name="chapters" value="{{ chapter.id }}"
                                   {% if chapter.is_main_chapter %}class="main-chapter"{% endif %}>
                            <span class="chapter-label">
                                {{ chapter.id }} - {{ chapter.name }}
                                {% if chapter.is_main_chapter %}<strong>(Főfejezet)</strong>{% endif %}
                            </span>
                        </label>
                    </div>
                    {% endfor %}
                </div>

                <div class="form-actions">
                    <button type="button" id="select-all" class="btn btn-secondary">Összes kiválasztása</button>
                    <button type="button" id="clear-all" class="btn btn-secondary">Törlés</button>
                </div>
            </form>
        </div>

        <div class="learning-types-selection">
            <h3>Learning Card típusok kiválasztása</h3>
            <p class="selection-info">Válaszd ki, milyen típusú tanulókártyákat szeretnél használni:</p>
            <form id="learning-types-form">
                <div class="types-grid">
                    {% if learning_card_types %}
                        {% for type in learning_card_types %}
                        <div class="type-item">
                            <label>
                                <input type="checkbox" name="learning_types" value="{{ type }}" checked>
                                <span class="type-label">{{ type }}</span>
                            </label>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="type-item">
                            <label>
                                <input type="checkbox" name="learning_types" value="Definition Card" checked>
                                <span class="type-label">Definition Card</span>
                            </label>
                        </div>
                        <div class="type-item">
                            <label>
                                <input type="checkbox" name="learning_types" value="Learning Card" checked>
                                <span class="type-label">Learning Card</span>
                            </label>
                        </div>
                        <div class="type-item">
                            <label>
                                <input type="checkbox" name="learning_types" value="Extra Deep" checked>
                                <span class="type-label">Extra Deep</span>
                            </label>
                        </div>
                    {% endif %}
                </div>

                <div class="form-actions">
                    <button type="button" id="select-all-types" class="btn btn-secondary">Összes típus</button>
                    <button type="button" id="clear-all-types" class="btn btn-secondary">Típusok törlése</button>
                </div>
            </form>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllBtn = document.getElementById('select-all');
    const clearAllBtn = document.getElementById('clear-all');
    const checkboxes = document.querySelectorAll('input[name="chapters"]');

    const selectAllTypesBtn = document.getElementById('select-all-types');
    const clearAllTypesBtn = document.getElementById('clear-all-types');
    const typeCheckboxes = document.querySelectorAll('input[name="learning_types"]');

    selectAllBtn.addEventListener('click', function() {
        checkboxes.forEach(cb => cb.checked = true);
    });

    clearAllBtn.addEventListener('click', function() {
        checkboxes.forEach(cb => cb.checked = false);
    });

    selectAllTypesBtn.addEventListener('click', function() {
        typeCheckboxes.forEach(cb => cb.checked = true);
    });

    clearAllTypesBtn.addEventListener('click', function() {
        typeCheckboxes.forEach(cb => cb.checked = false);
    });

    // Main chapter selection logic
    checkboxes.forEach(checkbox => {
        if (checkbox.classList.contains('main-chapter')) {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all sub-chapters of this main chapter
                    const mainChapterId = this.value;
                    checkboxes.forEach(cb => {
                        if (cb.value.startsWith(mainChapterId + '.')) {
                            cb.checked = true;
                        }
                    });
                }
            });
        }
    });
});

function getSelectedChapters() {
    const checkboxes = document.querySelectorAll('input[name="chapters"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function getSelectedLearningTypes() {
    const checkboxes = document.querySelectorAll('input[name="learning_types"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function startLearningMode() {
    const selectedChapters = getSelectedChapters();
    const selectedTypes = getSelectedLearningTypes();

    if (selectedChapters.length === 0) {
        alert('Kérlek válassz ki legalább egy fejezetet!');
        return;
    }

    if (selectedTypes.length === 0) {
        alert('Kérlek válassz ki legalább egy Learning Card típust!');
        return;
    }

    const params = new URLSearchParams();
    selectedChapters.forEach(chapter => params.append('chapters', chapter));
    selectedTypes.forEach(type => params.append('types', type));
    window.location.href = '/learning?' + params.toString();
}

function startPracticeMode() {
    const selectedChapters = getSelectedChapters();
    if (selectedChapters.length === 0) {
        alert('Kérlek válassz ki legalább egy fejezetet!');
        return;
    }

    const params = new URLSearchParams();
    selectedChapters.forEach(chapter => params.append('chapters', chapter));
    window.location.href = '/practice?' + params.toString();
}

function startReadingMode() {
    const selectedChapters = getSelectedChapters();
    if (selectedChapters.length === 0) {
        alert('Kérlek válassz ki legalább egy fejezetet!');
        return;
    }

    const params = new URLSearchParams();
    selectedChapters.forEach(chapter => params.append('chapters', chapter));
    window.location.href = '/reading?' + params.toString();
}

function startBigRandomMode() {
    window.location.href = '/big-random';
}
</script>
{% endblock %}
