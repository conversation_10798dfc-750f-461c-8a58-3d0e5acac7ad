{% extends "base.html" %}

{% block title %}LearnFast - Big Random{% endblock %}

{% block content %}
<div class="container">
    <div class="big-random-header">
        <h2>Big Random - Teljes Random <PERSON>ás</h2>
        <div class="progress-info">
            <span id="current-question">1</span> / <span id="total-questions">{{ total_questions }}</span>
            <span class="question-type" id="question-type">{{ current_question.type if current_question else 'Ismeretlen' }}</span>
        </div>
    </div>

    <div class="big-random-card" id="big-random-card">
        <!-- Dynamic content will be loaded here by JavaScript -->
        <div class="card-content" id="dynamic-content">
            <div class="question-section">
                <h3 id="question-title">Loading...</h3>
                <div class="question-text" id="question-en">
                    Loading question...
                </div>
                <div class="question-text-hu" id="question-hu" style="display: none;">
                    Loading question...
                </div>
                <button class="btn btn-info" id="show-question-hu">Mutasd magyarul</button>
            </div>

            <!-- Learning Card specific sections -->
            <div class="answer-section" id="answer-section" style="display: none;">
                <h3>Válasz:</h3>
                <div class="answer-text" id="answer-en">
                    Loading answer...
                </div>
                <div class="answer-text-hu" id="answer-hu" style="display: none;">
                    Loading answer...
                </div>
                <button class="btn btn-info" id="show-answer-hu" style="display: none;">Mutasd a választ magyarul</button>
            </div>

            <!-- Practice Quiz specific sections -->
            <div class="options-section" id="options-section" style="display: none;">
                <h3>Válaszlehetőségek:</h3>
                <div class="options-grid">
                    <div class="option-item">
                        <label>
                            <input type="radio" name="answer" value="" id="option1">
                            <span class="option-text">
                                <span class="option-en" id="option1-en">A) Loading...</span>
                                <span class="option-hu" id="option1-hu" style="display: none;">A) Loading...</span>
                            </span>
                        </label>
                    </div>
                    <div class="option-item">
                        <label>
                            <input type="radio" name="answer" value="" id="option2">
                            <span class="option-text">
                                <span class="option-en" id="option2-en">B) Loading...</span>
                                <span class="option-hu" id="option2-hu" style="display: none;">B) Loading...</span>
                            </span>
                        </label>
                    </div>
                    <div class="option-item">
                        <label>
                            <input type="radio" name="answer" value="" id="option3">
                            <span class="option-text">
                                <span class="option-en" id="option3-en">C) Loading...</span>
                                <span class="option-hu" id="option3-hu" style="display: none;">C) Loading...</span>
                            </span>
                        </label>
                    </div>
                    <div class="option-item">
                        <label>
                            <input type="radio" name="answer" value="" id="option4">
                            <span class="option-text">
                                <span class="option-en" id="option4-en">D) Loading...</span>
                                <span class="option-hu" id="option4-hu" style="display: none;">D) Loading...</span>
                            </span>
                        </label>
                    </div>
                </div>
                <button class="btn btn-info" id="show-options-hu">Mutasd a válaszokat magyarul</button>
            </div>

            <div class="solution-section" id="solution-section" style="display: none;">
                <h3>Megoldás:</h3>
                <div class="correct-answer" id="correct-answer-en">
                    Loading solution...
                </div>
                <div class="correct-answer-hu" id="correct-answer-hu" style="display: none;">
                    Loading solution...
                </div>
                <button class="btn btn-info" id="show-solution-hu" style="display: none;">Mutasd a megoldást magyarul</button>

                <div class="result-feedback" id="result-feedback">
                    <!-- This will be filled by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Universal action buttons -->
        <div class="card-actions" id="card-actions">
            <!-- Learning Card buttons -->
            <button class="btn btn-secondary" id="show-answer" style="display: none;">Mutasd a választ</button>

            <!-- Practice Quiz buttons -->
            <button class="btn btn-primary" id="check-answer" style="display: none;">Válasz ellenőrzése</button>

            <!-- Universal buttons -->
            <button class="btn btn-info" id="show-content">Mutasd a tananyagot</button>
            <button class="btn btn-primary" id="next-question">Következő kérdés</button>
        </div>

        <div class="chapter-content-section" id="chapter-content-section" style="display: none;">
            <h3>Tananyag - <span id="content-chapter-title">{{ current_question.chapter if current_question else '' }}</span>:</h3>
            <div class="content-text" id="content-en">
                <!-- This will be filled by JavaScript -->
            </div>
            <div class="content-text-hu" id="content-hu" style="display: none;">
                <!-- This will be filled by JavaScript -->
            </div>
            <button class="btn btn-info" id="show-content-hu" style="display: none;">Mutasd a tananyagot magyarul</button>
        </div>
    </div>

    <div class="big-random-controls">
        <a href="{{ url_for('index') }}" class="btn btn-secondary">Vissza a főoldalra</a>
        <button class="btn btn-warning" id="restart-big-random">Újrakezdés</button>
        <button class="btn btn-info" id="shuffle-questions">Újrakeverés</button>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" id="questions-data">
    {{ questions_json | safe }}
</script>
<script type="application/json" id="contents-data">
    {{ contents_json | safe }}
</script>
{% endblock %}

{% block scripts %}
<script>




document.addEventListener('DOMContentLoaded', function() {
    const questionsData = JSON.parse(document.getElementById('questions-data').textContent);
    const contentsData = JSON.parse(document.getElementById('contents-data').textContent);

    let currentQuestionIndex = 0;
    let selectedAnswer = null;
    
    // Elements
    const questionEn = document.getElementById('question-en');
    const questionHu = document.getElementById('question-hu');
    const questionTitle = document.getElementById('question-title');
    const answerSection = document.getElementById('answer-section');
    const answerEn = document.getElementById('answer-en');
    const answerHu = document.getElementById('answer-hu');
    const optionsSection = document.getElementById('options-section');
    const solutionSection = document.getElementById('solution-section');
    const correctAnswerEn = document.getElementById('correct-answer-en');
    const correctAnswerHu = document.getElementById('correct-answer-hu');
    const resultFeedback = document.getElementById('result-feedback');
    const contentSection = document.getElementById('chapter-content-section');
    const contentEn = document.getElementById('content-en');
    const contentHu = document.getElementById('content-hu');
    const contentChapterTitle = document.getElementById('content-chapter-title');

    const showQuestionHuBtn = document.getElementById('show-question-hu');
    const showOptionsHuBtn = document.getElementById('show-options-hu');
    const showAnswerBtn = document.getElementById('show-answer');
    const showAnswerHuBtn = document.getElementById('show-answer-hu');
    const checkAnswerBtn = document.getElementById('check-answer');
    const showSolutionHuBtn = document.getElementById('show-solution-hu');
    const showContentBtn = document.getElementById('show-content');
    const showContentHuBtn = document.getElementById('show-content-hu');
    const nextQuestionBtn = document.getElementById('next-question');
    const restartBtn = document.getElementById('restart-big-random');
    const shuffleBtn = document.getElementById('shuffle-questions');

    const currentQuestionSpan = document.getElementById('current-question');
    const questionTypeSpan = document.getElementById('question-type');

    // Option elements
    const option1 = document.getElementById('option1');
    const option2 = document.getElementById('option2');
    const option3 = document.getElementById('option3');
    const option4 = document.getElementById('option4');
    const option1En = document.getElementById('option1-en');
    const option2En = document.getElementById('option2-en');
    const option3En = document.getElementById('option3-en');
    const option4En = document.getElementById('option4-en');
    const option1Hu = document.getElementById('option1-hu');
    const option2Hu = document.getElementById('option2-hu');
    const option3Hu = document.getElementById('option3-hu');
    const option4Hu = document.getElementById('option4-hu');
    
    function loadQuestion(index) {
        if (index < 0 || index >= questionsData.length) return;

        const question = questionsData[index];
        selectedAnswer = null;

        // Reset all visibility
        questionHu.style.display = 'none';
        answerSection.style.display = 'none';
        answerHu.style.display = 'none';
        optionsSection.style.display = 'none';
        solutionSection.style.display = 'none';
        correctAnswerHu.style.display = 'none';
        contentSection.style.display = 'none';
        contentHu.style.display = 'none';

        // Reset buttons
        showAnswerBtn.style.display = 'none';
        showAnswerHuBtn.style.display = 'none';
        checkAnswerBtn.style.display = 'none';
        showSolutionHuBtn.style.display = 'none';
        showContentHuBtn.style.display = 'none';

        // Reset option visibility
        document.querySelectorAll('.option-hu').forEach(el => el.style.display = 'none');

        // Load basic content
        questionEn.textContent = question.question;
        questionHu.textContent = question.question_hu || question.question;
        questionTitle.textContent = `${question.type === 'learning' ? 'Learning Card' : 'Practice Quiz'} - ${question.chapter}:`;
        questionTypeSpan.textContent = question.type === 'learning' ? 'Learning Card' : 'Practice Quiz';

        if (question.type === 'learning') {
            // Learning Card setup
            answerEn.textContent = question.answer;
            answerHu.textContent = question.answer_hu;

            // Show learning card buttons
            showAnswerBtn.style.display = 'inline-block';

        } else {
            // Practice Quiz setup
            optionsSection.style.display = 'block';

            // Update options
            const options = [question.option1, question.option2, question.option3, question.option4];
            const optionsHu = [question.option1_hu, question.option2_hu, question.option3_hu, question.option4_hu];
            const letters = ['A', 'B', 'C', 'D'];

            option1En.textContent = `${letters[0]}) ${options[0]}`;
            option2En.textContent = `${letters[1]}) ${options[1]}`;
            option3En.textContent = `${letters[2]}) ${options[2]}`;
            option4En.textContent = `${letters[3]}) ${options[3]}`;

            option1Hu.textContent = `${letters[0]}) ${optionsHu[0]}`;
            option2Hu.textContent = `${letters[1]}) ${optionsHu[1]}`;
            option3Hu.textContent = `${letters[2]}) ${optionsHu[2]}`;
            option4Hu.textContent = `${letters[3]}) ${optionsHu[3]}`;

            option1.value = options[0];
            option2.value = options[1];
            option3.value = options[2];
            option4.value = options[3];

            option1.checked = false;
            option2.checked = false;
            option3.checked = false;
            option4.checked = false;

            correctAnswerEn.textContent = `Helyes válasz: ${question.answer}`;

            // Find the correct answer in Hungarian
            const correctIndex = options.indexOf(question.answer);
            if (correctIndex !== -1) {
                correctAnswerHu.textContent = `Helyes válasz: ${optionsHu[correctIndex]}`;
            }

            // Show practice quiz buttons
            checkAnswerBtn.style.display = 'inline-block';
        }

        // Load chapter content
        const content = contentsData.find(c => c.chapter === question.chapter);
        if (content) {
            contentEn.innerHTML = content.content_en;
            contentHu.innerHTML = content.content_hu;
        } else {
            contentEn.textContent = 'Nincs elérhető tananyag ehhez a fejezethez.';
            contentHu.textContent = 'Nincs elérhető magyar tananyag ehhez a fejezethez.';
        }
        contentChapterTitle.textContent = question.chapter;

        currentQuestionSpan.textContent = index + 1;
    }
    
    // Event listeners
    showQuestionHuBtn.addEventListener('click', function() {
        questionHu.style.display = questionHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = questionHu.style.display === 'none' ? 'Mutasd magyarul' : 'Rejtsd el a magyart';
    });

    showOptionsHuBtn.addEventListener('click', function() {
        const optionsHu = [option1Hu, option2Hu, option3Hu, option4Hu];
        const isHidden = optionsHu[0].style.display === 'none';

        optionsHu.forEach(el => el.style.display = isHidden ? 'block' : 'none');
        this.textContent = isHidden ? 'Rejtsd el a magyar válaszokat' : 'Mutasd a válaszokat magyarul';
    });

    showAnswerBtn.addEventListener('click', function() {
        answerSection.style.display = 'block';
        showAnswerHuBtn.style.display = 'inline-block';
    });

    showAnswerHuBtn.addEventListener('click', function() {
        answerHu.style.display = answerHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = answerHu.style.display === 'none' ? 'Mutasd a választ magyarul' : 'Rejtsd el a magyar választ';
    });

    // Radio button listeners
    [option1, option2, option3, option4].forEach(radio => {
        radio.addEventListener('change', function() {
            selectedAnswer = this.value;
        });
    });

    checkAnswerBtn.addEventListener('click', function() {
        if (!selectedAnswer) {
            alert('Kérlek válassz egy opciót!');
            return;
        }

        const question = questionsData[currentQuestionIndex];
        const isCorrect = selectedAnswer === question.answer;

        // Show feedback
        resultFeedback.innerHTML = isCorrect
            ? '<div class="feedback correct">✓ Helyes válasz!</div>'
            : '<div class="feedback incorrect">✗ Helytelen válasz!</div>';

        // Show solution immediately
        solutionSection.style.display = 'block';
        showSolutionHuBtn.style.display = 'inline-block';

        // Hide check button
        this.style.display = 'none';
    });



    showSolutionHuBtn.addEventListener('click', function() {
        correctAnswerHu.style.display = correctAnswerHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = correctAnswerHu.style.display === 'none' ? 'Mutasd a megoldást magyarul' : 'Rejtsd el a magyar megoldást';
    });

    showContentBtn.addEventListener('click', function() {
        contentSection.style.display = 'block';
        showContentHuBtn.style.display = 'inline-block';
    });

    showContentHuBtn.addEventListener('click', function() {
        contentHu.style.display = contentHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = contentHu.style.display === 'none' ? 'Mutasd a tananyagot magyarul' : 'Rejtsd el a magyar tananyagot';
    });

    nextQuestionBtn.addEventListener('click', function() {
        currentQuestionIndex = (currentQuestionIndex + 1) % questionsData.length;
        loadQuestion(currentQuestionIndex);
    });
    
    restartBtn.addEventListener('click', function() {
        currentQuestionIndex = 0;
        loadQuestion(currentQuestionIndex);
    });

    shuffleBtn.addEventListener('click', function() {
        // Shuffle the questions array
        for (let i = questionsData.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [questionsData[i], questionsData[j]] = [questionsData[j], questionsData[i]];
        }
        currentQuestionIndex = 0;
        loadQuestion(currentQuestionIndex);
    });
    
    // Initialize
    if (questionsData.length > 0) {
        loadQuestion(currentQuestionIndex);
    }
});
</script>
{% endblock %}
