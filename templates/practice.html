{% extends "base.html" %}

{% block title %}LearnFast - Practice Quiz{% endblock %}

{% block content %}
<div class="container">
    <div class="practice-header">
        <h2>Practice Quiz</h2>
        <div class="progress-info">
            <span id="current-question">1</span> / <span id="total-questions">{{ total_questions }}</span>
        </div>
    </div>

    <div class="practice-card" id="practice-card">
        <div class="question-section">
            <h3>Kérdés:</h3>
            <div class="question-text" id="question-en">
                {{ current_question.question }}
            </div>
            <div class="question-text-hu" id="question-hu" style="display: none;">
                {{ current_question.question }}
            </div>
            <button class="btn btn-info" id="show-question-hu">Mutasd magyarul</button>
        </div>

        <div class="options-section">
            <h3><PERSON><PERSON><PERSON><PERSON>hetőségek:</h3>
            <div class="options-grid">
                <div class="option-item">
                    <label>
                        <input type="radio" name="answer" value="{{ current_question.option1 }}">
                        <span class="option-text">
                            <span class="option-en">A) {{ current_question.option1 }}</span>
                            <span class="option-hu" style="display: none;">A) {{ current_question.option1_hu }}</span>
                        </span>
                    </label>
                </div>
                <div class="option-item">
                    <label>
                        <input type="radio" name="answer" value="{{ current_question.option2 }}">
                        <span class="option-text">
                            <span class="option-en">B) {{ current_question.option2 }}</span>
                            <span class="option-hu" style="display: none;">B) {{ current_question.option2_hu }}</span>
                        </span>
                    </label>
                </div>
                <div class="option-item">
                    <label>
                        <input type="radio" name="answer" value="{{ current_question.option3 }}">
                        <span class="option-text">
                            <span class="option-en">C) {{ current_question.option3 }}</span>
                            <span class="option-hu" style="display: none;">C) {{ current_question.option3_hu }}</span>
                        </span>
                    </label>
                </div>
                <div class="option-item">
                    <label>
                        <input type="radio" name="answer" value="{{ current_question.option4 }}">
                        <span class="option-text">
                            <span class="option-en">D) {{ current_question.option4 }}</span>
                            <span class="option-hu" style="display: none;">D) {{ current_question.option4_hu }}</span>
                        </span>
                    </label>
                </div>
            </div>
            <button class="btn btn-info" id="show-options-hu">Mutasd a válaszokat magyarul</button>
        </div>

        <div class="solution-section" id="solution-section" style="display: none;">
            <h3>Megoldás:</h3>
            <div class="correct-answer" id="correct-answer-en">
                Helyes válasz: {{ current_question.answer }}
            </div>
            <div class="correct-answer-hu" id="correct-answer-hu" style="display: none;">
                <!-- This will be filled by JavaScript -->
            </div>
            <button class="btn btn-info" id="show-solution-hu" style="display: none;">Mutasd a megoldást magyarul</button>

            <div class="result-feedback" id="result-feedback">
                <!-- This will be filled by JavaScript -->
            </div>
        </div>

        <div class="chapter-content-section" id="chapter-content-section" style="display: none;">
            <h3>Tananyag - <span id="content-chapter-title">{{ current_question.chapter }}</span>:</h3>
            <div class="content-text" id="content-en">
                <!-- This will be filled by JavaScript -->
            </div>
            <div class="content-text-hu" id="content-hu" style="display: none;">
                <!-- This will be filled by JavaScript -->
            </div>
            <button class="btn btn-info" id="show-content-hu" style="display: none;">Mutasd a tananyagot magyarul</button>
        </div>

        <div class="practice-actions">
            <button class="btn btn-primary" id="check-answer">Válasz ellenőrzése</button>
            <button class="btn btn-info" id="show-content">Mutasd a tananyagot</button>
            <button class="btn btn-primary" id="next-question" style="display: none;">Következő kérdés</button>
        </div>
    </div>

    <div class="practice-controls">
        <a href="{{ url_for('index') }}" class="btn btn-secondary">Vissza a főoldalra</a>
        <button class="btn btn-warning" id="restart-practice">Újrakezdés</button>
    </div>
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" id="questions-data">
    {{ questions_json | safe }}
</script>
<script type="application/json" id="contents-data">
    {{ contents_json | safe }}
</script>
{% endblock %}

{% block scripts %}
<script>




document.addEventListener('DOMContentLoaded', function() {
    const questionsData = JSON.parse(document.getElementById('questions-data').textContent);
    const contentsData = JSON.parse(document.getElementById('contents-data').textContent);

    let currentQuestionIndex = 0;
    let selectedAnswer = null;
    
    // Elements
    const questionEn = document.getElementById('question-en');
    const questionHu = document.getElementById('question-hu');
    const solutionSection = document.getElementById('solution-section');
    const correctAnswerEn = document.getElementById('correct-answer-en');
    const correctAnswerHu = document.getElementById('correct-answer-hu');
    const resultFeedback = document.getElementById('result-feedback');
    const contentSection = document.getElementById('chapter-content-section');
    const contentEn = document.getElementById('content-en');
    const contentHu = document.getElementById('content-hu');
    const contentChapterTitle = document.getElementById('content-chapter-title');
    
    const showQuestionHuBtn = document.getElementById('show-question-hu');
    const showOptionsHuBtn = document.getElementById('show-options-hu');
    const checkAnswerBtn = document.getElementById('check-answer');
    const showSolutionHuBtn = document.getElementById('show-solution-hu');
    const showContentBtn = document.getElementById('show-content');
    const showContentHuBtn = document.getElementById('show-content-hu');
    const nextQuestionBtn = document.getElementById('next-question');
    const restartBtn = document.getElementById('restart-practice');
    
    const currentQuestionSpan = document.getElementById('current-question');
    const answerRadios = document.querySelectorAll('input[name="answer"]');
    
    function loadQuestion(index) {
        const question = questionsData[index];
        
        // Reset visibility and state
        questionHu.style.display = 'none';
        solutionSection.style.display = 'none';
        correctAnswerHu.style.display = 'none';
        contentSection.style.display = 'none';
        contentHu.style.display = 'none';
        showSolutionHuBtn.style.display = 'none';
        showContentHuBtn.style.display = 'none';
        nextQuestionBtn.style.display = 'none';
        checkAnswerBtn.style.display = 'inline-block';
        
        document.querySelectorAll('.option-hu').forEach(el => el.style.display = 'none');
        answerRadios.forEach(radio => radio.checked = false);
        selectedAnswer = null;
        
        // Load content
        questionEn.textContent = question.question;
        questionHu.textContent = question.question;
        
        // Update options
        const optionTexts = document.querySelectorAll('.option-text');
        const options = [question.option1, question.option2, question.option3, question.option4];
        const optionsHu = [question.option1_hu, question.option2_hu, question.option3_hu, question.option4_hu];
        const letters = ['A', 'B', 'C', 'D'];
        
        optionTexts.forEach((optionText, i) => {
            const optionEn = optionText.querySelector('.option-en');
            const optionHu = optionText.querySelector('.option-hu');
            optionEn.textContent = `${letters[i]}) ${options[i]}`;
            optionHu.textContent = `${letters[i]}) ${optionsHu[i]}`;
            
            // Update radio value
            answerRadios[i].value = options[i];
        });
        
        correctAnswerEn.textContent = `Helyes válasz: ${question.answer}`;
        
        // Find the correct answer in Hungarian
        const correctIndex = options.indexOf(question.answer);
        if (correctIndex !== -1) {
            correctAnswerHu.textContent = `Helyes válasz: ${optionsHu[correctIndex]}`;
        }

        // Load chapter content
        const content = contentsData.find(c => c.chapter === question.chapter);
        if (content) {
            contentEn.innerHTML = content.content_en;
            contentHu.innerHTML = content.content_hu;
        } else {
            contentEn.textContent = 'Nincs elérhető tananyag ehhez a fejezethez.';
            contentHu.textContent = 'Nincs elérhető magyar tananyag ehhez a fejezethez.';
        }
        contentChapterTitle.textContent = question.chapter;

        currentQuestionSpan.textContent = index + 1;
    }
    
    // Event listeners
    showQuestionHuBtn.addEventListener('click', function() {
        questionHu.style.display = questionHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = questionHu.style.display === 'none' ? 'Mutasd magyarul' : 'Rejtsd el a magyart';
    });
    
    showOptionsHuBtn.addEventListener('click', function() {
        const optionsHu = document.querySelectorAll('.option-hu');
        const isHidden = optionsHu[0].style.display === 'none';
        
        optionsHu.forEach(el => el.style.display = isHidden ? 'block' : 'none');
        this.textContent = isHidden ? 'Rejtsd el a magyar válaszokat' : 'Mutasd a válaszokat magyarul';
    });
    
    answerRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            selectedAnswer = this.value;
        });
    });
    
    checkAnswerBtn.addEventListener('click', function() {
        if (!selectedAnswer) {
            alert('Kérlek válassz egy opciót!');
            return;
        }

        const question = questionsData[currentQuestionIndex];
        const isCorrect = selectedAnswer === question.answer;

        // Show feedback
        resultFeedback.innerHTML = isCorrect
            ? '<div class="feedback correct">✓ Helyes válasz!</div>'
            : '<div class="feedback incorrect">✗ Helytelen válasz!</div>';

        // Show solution immediately
        solutionSection.style.display = 'block';
        showSolutionHuBtn.style.display = 'inline-block';
        nextQuestionBtn.style.display = 'inline-block';

        // Hide check button
        this.style.display = 'none';
    });


    
    showSolutionHuBtn.addEventListener('click', function() {
        correctAnswerHu.style.display = correctAnswerHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = correctAnswerHu.style.display === 'none' ? 'Mutasd a megoldást magyarul' : 'Rejtsd el a magyar megoldást';
    });

    showContentBtn.addEventListener('click', function() {
        contentSection.style.display = 'block';
        showContentHuBtn.style.display = 'inline-block';
    });

    showContentHuBtn.addEventListener('click', function() {
        contentHu.style.display = contentHu.style.display === 'none' ? 'block' : 'none';
        this.textContent = contentHu.style.display === 'none' ? 'Mutasd a tananyagot magyarul' : 'Rejtsd el a magyar tananyagot';
    });
    
    nextQuestionBtn.addEventListener('click', function() {
        currentQuestionIndex = (currentQuestionIndex + 1) % questionsData.length;
        loadQuestion(currentQuestionIndex);
    });
    
    restartBtn.addEventListener('click', function() {
        currentQuestionIndex = 0;
        loadQuestion(currentQuestionIndex);
    });
});
</script>
{% endblock %}
