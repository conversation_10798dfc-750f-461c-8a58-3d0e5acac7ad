# LearnFast Fájlstruktúra Dokumentáció

## 📁 Új Fájlstruktúra (2025-06-27 után)

```
books/
└── [TANANYAG_NEVE]/           # Minden tananyag saját mappában
    ├── chapters.xlsx          # Fejezetek listája (tananyagonként)
    ├── learning.xlsx          # Tanulókártyák (tananyagonként)
    ├── practice.xlsx          # Gyakorló <PERSON> (tananyagonként)
    └── DATA/                  # Tananyag szövegek
        ├── 1.1.txt
        ├── 1.2.txt
        ├── 2.1.txt
        └── ...
```

## 🔄 Változások

### El<PERSON><PERSON> (régi struktúra):
```
books/
├── chapters.xlsx              # ❌ Közös fájl minden tananyaghoz
├── learning.xlsx              # ❌ Közös fájl minden tananyaghoz
├── practice.xlsx              # ❌ Közös fájl minden tananyaghoz
└── CT-AI/
    └── DATA/
        ├── 1.1.txt
        └── ...
```

### <PERSON><PERSON><PERSON><PERSON> (új struktúra):
```
books/
└── CT-AI/                     # ✅ Minden fájl a tananyag mappájában
    ├── chapters.xlsx          # ✅ CT-AI specifikus fejezetek
    ├── learning.xlsx          # ✅ CT-AI specifikus tanulókártyák
    ├── practice.xlsx          # ✅ CT-AI specifikus gyakorló kérdések
    └── DATA/
        ├── 1.1.txt
        └── ...
```

## 🎯 Előnyök

### ✅ Független tananyagok
- Minden tananyag saját Excel fájlokkal
- Nincs ütközés különböző tananyagok között
- Könnyebb karbantartás

### ✅ Skálázhatóság
- Új tananyag hozzáadása egyszerű
- Minden tananyag külön kezelhető
- Nincs szükség központi fájlok módosítására

### ✅ Biztonság
- Egy tananyag módosítása nem érinti a többit
- Backup és restore tananyagonként
- Verziókezelés tananyagonként

## 📝 Példa: Új tananyag hozzáadása

### 1. Mappa létrehozása:
```
books/
└── MATH-101/                  # Új tananyag
    ├── chapters.xlsx          # MATH-101 fejezetek
    ├── learning.xlsx          # MATH-101 tanulókártyák
    ├── practice.xlsx          # MATH-101 gyakorló kérdések
    └── DATA/
        ├── 1.1.txt            # Algebra alapok
        ├── 1.2.txt            # Egyenletek
        └── ...
```

### 2. Excel fájlok kitöltése:
- `chapters.xlsx`: MATH-101 specifikus fejezetek
- `learning.xlsx`: MATH-101 specifikus tanulókártyák  
- `practice.xlsx`: MATH-101 specifikus gyakorló kérdések

### 3. Automatikus felismerés:
- Az alkalmazás automatikusan felismeri az új tananyagot
- Nincs szükség kód módosításra
- Azonnal használható

## 🔧 Technikai implementáció

### DataLoader módosítások:
```python
# Előtte
chapters_file = os.path.join(self.books_path, "chapters.xlsx")

# Utána  
chapters_file = os.path.join(subject_path, "chapters.xlsx")
```

### Érintett metódusok:
- `_load_chapters(subject_path)`
- `_load_learning_cards(subject_path)`
- `_load_practice_questions(subject_path)`

## ✅ Tesztelés eredménye

### Minden funkció működik:
- ✅ Subject selection: 200 OK
- ✅ Learning Cards: Content loaded
- ✅ Practice Quiz: Content loaded  
- ✅ Reading Mode: Content loaded
- ✅ Big Random: Content loaded

### Adatok betöltése:
- ✅ Chapters: 3 fejezet
- ✅ Learning cards: 6 tanulókártya
- ✅ Practice questions: 6 gyakorló kérdés
- ✅ Chapter contents: 3 tananyag

## 🎉 Összefoglalás

A **fájlstruktúra átszervezés sikeresen elkészült**:

- ✅ **Minden tananyag független**
- ✅ **Skálázható architektúra**
- ✅ **Könnyű karbantartás**
- ✅ **Biztonságos működés**
- ✅ **Backward compatible** (régi CT-AI tananyag továbbra is működik)

**Az alkalmazás most felkészült több tananyag kezelésére!** 🚀
