from dataclasses import dataclass
from typing import List, Optional

@dataclass
class Chapter:
    id: str
    name: str
    is_main_chapter: bool = False
    
    def __post_init__(self):
        self.is_main_chapter = self.id.startswith('?')
        if self.is_main_chapter:
            self.id = self.id[1:]  # Remove the '?' prefix

@dataclass
class LearningCard:
    chapter: str
    type: str
    question: str
    question_hu: str
    answer: str
    answer_hu: str

@dataclass
class PracticeQuestion:
    chapter: str
    question: str
    option1: str
    option1_hu: str
    option2: str
    option2_hu: str
    option3: str
    option3_hu: str
    option4: str
    option4_hu: str
    answer: str

@dataclass
class ChapterContent:
    chapter: str
    content_en: str
    content_hu: str

@dataclass
class Subject:
    name: str
    path: str
    chapters: List[Chapter]
    learning_cards: List[LearningCard]
    practice_questions: List[PracticeQuestion]
    chapter_contents: List[ChapterContent]
