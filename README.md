# LearnFast - Tanulás Segítő Program

## Leírás

A LearnFast egy webalapú tanulás segítő alkalmazás, amely Excel fájlokból olvas be tananyagokat és különböző tanulási módokat kínál.

## Funkciók

### 1. Tananyag kezelés
- A `books` mappában lévő tananyagok automatikus felismerése
- Jelenleg támogatott: CT-AI tananyag

### 2. Tanulási módok

#### Learning Cards (Tanulókártyák)
- Kérdés-válasz alapú tanulás
- Angol és magyar nyelvű tartalom
- Random sorrendű kérdések
- Tananyag megjelenítése fejezetek szerint
- **ÚJ**: Típus alapú szűrés (Definition Card, Learning Card, Extra Deep)

#### Practice Quiz (Gyakorló kvíz)
- 4 válaszlehetőséges kérdések
- Azonnali visszajelzés
- Angol és magyar nyelvű opciók
- **ÚJ**: Tananyag megjelenítése kvíz közben

#### **ÚJ**: Tananyag Olvasás
- Fejezetek szerinti olvasás
- Fa struktúra navigáció (jobb oldali sidebar)
- Angol/magyar nyelvváltás
- Következő/előző fejezet navigáció
- Közvetlen ugrás fejezetekre

#### **ÚJ**: Big Random
- Minden kérdés összekeverve (Learning Cards + Practice Quiz)
- Teljes random tanulás
- Típus jelzés minden kérdésnél
- Újrakeverés funkció

### 3. Fejezet kiválasztás
- Főfejezetek és alfejezetek
- Rugalmas kiválasztási lehetőségek
- Főfejezet kiválasztása automatikusan kiválasztja az alfejezeteket

### 4. Learning Card típus kiválasztás
- **Definition Card**: Definíciók és alapfogalmak
- **Learning Card**: Általános tanulókártyák
- **Extra Deep**: Mélyebb, részletesebb tartalom
- Rugalmas típus kombinációk

### 5. **ÚJ**: Tananyag olvasás navigáció
- Fa struktúra a jobb oldalon
- Főfejezetek és alfejezetek megjelenítése
- Közvetlen ugrás bármely fejezetre
- Vizuális jelzés az elérhető tartalomról

### 6. **ÚJ**: Big Random mód
- Minden tanulási anyag összekeverve
- Learning Cards és Practice Quiz vegyesen
- Típus alapú megkülönböztetés
- Újrakeverés lehetőség

## Fájl struktúra

```
books/
└── [TANANYAG_NEVE]/       # pl. CT-AI
    ├── chapters.xlsx      # Fejezetek listája (tananyagonként)
    ├── learning.xlsx      # Tanulókártyák (tananyagonként)
    ├── practice.xlsx      # Gyakorló kérdések (tananyagonként)
    └── DATA/
        ├── 1.1.txt        # Fejezet tartalom
        ├── 1.2.txt
        └── ...
```

**Fontos**: Minden tananyagnak saját Excel fájljai vannak a saját mappájában!

### Excel fájlok (tananyag mappájában)

#### chapters.xlsx
- Oszlop 1: `id` (? = főfejezet, pl. ?1, 1.1, 1.2)
- Oszlop 2: `name` (fejezet neve)

#### learning.xlsx
- Oszlop 1: `chapter` (fejezet azonosító)
- Oszlop 2: `type` (tanulókártya típusa)
- Oszlop 3: `question` (kérdés angolul)
- Oszlop 4: `question hu` (kérdés magyarul)
- Oszlop 5: `answer` (válasz angolul)
- Oszlop 6: `answer hu` (válasz magyarul)

#### practice.xlsx
- Oszlop 1: `chapter` (fejezet azonosító)
- Oszlop 2: `question` (kérdés)
- Oszlop 3-6: `option 1-4` (válaszlehetőségek angolul)
- Oszlop 7-10: `option 1-4 hu` (válaszlehetőségek magyarul)
- Oszlop 11: `answer` (helyes válasz)

### DATA mappa
- Fejezet azonosító alapján elnevezett .txt fájlok (pl. 1.1.txt)
- Formátum:
  ```
  EN:
  Angol szöveg

  HU:
  Magyar szöveg
  ```

## Telepítés és futtatás

### 1. Függőségek telepítése
```bash
pip install -r requirements.txt
```

### 2. Alkalmazás indítása
```bash
python app.py
```

### 3. Böngészőben megnyitás
Nyisd meg a böngészőt és menj a következő címre:
```
http://127.0.0.1:5000
```

## Használat

### 1. Tananyag kiválasztása
- A főoldalon válaszd ki a kívánt tananyagot (jelenleg: CT-AI)

### 2. Fejezetek kiválasztása
- Jelöld be a tanulni kívánt fejezeteket
- Főfejezet kiválasztása automatikusan kiválasztja az alfejezeteket
- "Összes kiválasztása" és "Törlés" gombok a gyors kiválasztáshoz

### 3. Learning Card típusok kiválasztása
- Válaszd ki a kívánt tanulókártya típusokat:
  - **Definition Card**: Definíciók és alapfogalmak
  - **Learning Card**: Általános tanulókártyák
  - **Extra Deep**: Mélyebb, részletesebb tartalom
- "Összes típus" és "Típusok törlése" gombok a gyors kiválasztáshoz

### 4. Tanulási mód kiválasztása

#### Learning Cards
- Kattints a "Learning Cards" indítás gombra
- Olvasd el a kérdést (angol)
- "Mutasd magyarul" - magyar fordítás megjelenítése
- "Mutasd a választ" - válasz megjelenítése
- "Mutasd a tananyagot" - kapcsolódó tananyag megjelenítése
- "Következő kérdés" - következő kártyára lépés

#### Practice Quiz
- Kattints a "Practice Quiz" indítás gombra
- Olvasd el a kérdést és válaszlehetőségeket
- "Mutasd magyarul" - magyar fordítások megjelenítése
- Válassz egy opciót
- **JAVÍTVA**: "Válasz ellenőrzése" - egy gombnyomással visszajelzés + helyes válasz
- **ÚJ**: "Mutasd a tananyagot" - kapcsolódó tananyag megjelenítése kvíz közben
- "Következő kérdés" - következő kérdésre lépés

#### **ÚJ**: Tananyag Olvasás
- Kattints a "Tananyag Olvasás" indítás gombra
- Olvasd végig a tananyagot fejezetek szerint
- Jobb oldali fa struktúra navigáció:
  - Főfejezetek és alfejezetek listája
  - Kattints bármely fejezetre az azonnali ugráshoz
  - Aktív fejezet kiemelése
  - Elérhető tartalom jelzése
- "Mutasd magyarul" - magyar verzió megjelenítése
- "Előző/Következő fejezet" - szekvenciális navigáció

#### **ÚJ**: Big Random
- Kattints a "Big Random" indítás gombra
- Minden kérdés összekeverve (Learning Cards + Practice Quiz)
- Típus jelzés minden kérdésnél
- Ugyanazok a funkciók, mint az eredeti módokban
- "Újrakeverés" - kérdések újrakeverése
- "Újrakezdés" - visszatérés az elejére

## Technikai részletek

### Backend
- **Flask** - Python web framework
- **pandas** - Excel fájlok olvasása
- **openpyxl** - Excel fájl támogatás

### Frontend
- **HTML5** - Struktúra
- **CSS3** - Stílusok és reszponzív design
- **JavaScript** - Interaktivitás

### Adatkezelés
- Session alapú állapotkezelés
- JSON API végpontok
- Automatikus adatvalidáció

## Fejlesztési lehetőségek

1. **Új tananyagok hozzáadása**: Egyszerűen hozz létre új mappát a `books` könyvtárban
2. **Többnyelvű támogatás**: További nyelvek hozzáadása
3. **Haladás követése**: Tanulási statisztikák
4. **Exportálás**: Eredmények mentése
5. **Felhasználói fiókok**: Személyre szabott tanulás

## Legutóbbi javítások

### ✅ Fájlstruktúra átszervezés
- **Változás**: Excel fájlok (`chapters.xlsx`, `learning.xlsx`, `practice.xlsx`) most minden tananyag saját mappájában
- **Előtte**: `books/chapters.xlsx` (közös)
- **Utána**: `books/CT-AI/chapters.xlsx` (tananyagonként)
- **Eredmény**: ✅ Minden tananyag független, könnyebb kezelhetőség

### ✅ Tananyag címek vastagbetűs formázása
- **Funkció**: Az első sor (cím) automatikusan **vastagbetűvel** jelenik meg
- **Megoldás**: Backend HTML generálás + `white-space: pre-line` CSS
- **Eredmény**: ✅ **MINDEN módban tökéletesen működik** (Learning Cards, Practice Quiz, Reading, Big Random)
- **Példa**: "Definition of AI and AI Effect" → **Definition of AI and AI Effect**
- **Technikai**: HTML escape-elés + sortörések megőrzése

### ✅ Practice Quiz egyszerűsítés
- **Probléma**: Két gombnyomás kellett a megoldáshoz ("Válasz ellenőrzése" → "Megoldás")
- **Megoldás**: Egy gombnyomással mindent megmutat (visszajelzés + helyes válasz)
- **Eredmény**: Gyorsabb és egyszerűbb használat

### ✅ Sortörések megőrzése
- **Probléma**: A tananyag szövegekben nem jelentek meg a sortörések
- **Megoldás**: `white-space: pre-wrap` CSS beállítás hozzáadva
- **Eredmény**: A .txt fájlokban lévő enterek most már megjelennek

### ✅ Big Random következő gomb
- **Probléma**: A Big Random módban hiányzott a következő gomb
- **Megoldás**: Külön gombok Learning Card és Practice Quiz típusokhoz
- **Eredmény**: Mindkét típusnál működik a következő kérdés funkció

## Hibaelhárítás

### Excel fájlok nem töltődnek be
- Ellenőrizd, hogy a fájlok a `books` mappában vannak
- Győződj meg róla, hogy a fájlformátum .xlsx
- Ellenőrizd az oszlopok sorrendjét

### Alkalmazás nem indul
- Ellenőrizd, hogy minden függőség telepítve van
- Futtasd: `pip install -r requirements.txt`
- Ellenőrizd a Python verziót (3.8+)

### Tananyag nem jelenik meg
- Ellenőrizd a DATA mappa létezését
- Győződj meg róla, hogy a .txt fájlok megfelelő formátumban vannak
- A sortörések automatikusan megőrződnek

## Licenc

Ez a projekt oktatási célokra készült.
