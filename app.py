from flask import Flask, render_template, request, session, redirect, url_for, jsonify
import json
import random
from data_loader import DataLoader
from models import Subject

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Initialize data loader
data_loader = DataLoader()

@app.route('/')
def index():
    """Main page - subject selection and mode selection"""
    subjects = data_loader.get_available_subjects()
    
    chapters = []
    if session.get('current_subject'):
        try:
            subject_data = data_loader.load_subject(session['current_subject'])
            chapters = subject_data.chapters
        except Exception as e:
            print(f"Error loading subject data: {e}")
            session.pop('current_subject', None)
    
    return render_template('index.html', subjects=subjects, chapters=chapters)

@app.route('/select_subject', methods=['POST'])
def select_subject():
    """Handle subject selection"""
    subject_name = request.form.get('subject')
    if subject_name:
        session['current_subject'] = subject_name
        try:
            # Load and store subject data in session
            subject_data = data_loader.load_subject(subject_name)
            session['subject_data'] = {
                'name': subject_data.name,
                'chapters': [{'id': c.id, 'name': c.name, 'is_main_chapter': c.is_main_chapter} for c in subject_data.chapters],
                'learning_cards': [
                    {
                        'chapter': lc.chapter,
                        'type': lc.type,
                        'question': lc.question,
                        'question_hu': lc.question_hu,
                        'answer': lc.answer,
                        'answer_hu': lc.answer_hu
                    } for lc in subject_data.learning_cards
                ],
                'practice_questions': [
                    {
                        'chapter': pq.chapter,
                        'question': pq.question,
                        'option1': pq.option1,
                        'option1_hu': pq.option1_hu,
                        'option2': pq.option2,
                        'option2_hu': pq.option2_hu,
                        'option3': pq.option3,
                        'option3_hu': pq.option3_hu,
                        'option4': pq.option4,
                        'option4_hu': pq.option4_hu,
                        'answer': pq.answer
                    } for pq in subject_data.practice_questions
                ],
                'chapter_contents': [
                    {
                        'chapter': cc.chapter,
                        'content_en': cc.content_en,
                        'content_hu': cc.content_hu
                    } for cc in subject_data.chapter_contents
                ]
            }
        except Exception as e:
            print(f"Error loading subject: {e}")
            return redirect(url_for('index'))
    
    return redirect(url_for('index'))

@app.route('/learning')
def learning_mode():
    """Learning cards mode"""
    if not session.get('current_subject') or not session.get('subject_data'):
        return redirect(url_for('index'))
    
    # Get selected chapters from request args or use all chapters
    selected_chapters = request.args.getlist('chapters')
    if not selected_chapters:
        # If no chapters selected, use all available chapters
        selected_chapters = [c['id'] for c in session['subject_data']['chapters'] if not c['is_main_chapter']]
    
    # Filter learning cards by selected chapters
    all_cards = session['subject_data']['learning_cards']
    filtered_cards = [card for card in all_cards if card['chapter'] in selected_chapters]
    
    if not filtered_cards:
        return redirect(url_for('index'))
    
    # Shuffle cards for random order
    random.shuffle(filtered_cards)
    
    # Get current card (first one)
    current_card = filtered_cards[0]
    
    # Find chapter content for current card
    chapter_contents = session['subject_data']['chapter_contents']
    chapter_content = next((cc for cc in chapter_contents if cc['chapter'] == current_card['chapter']), None)
    
    return render_template('learning.html',
                         current_card=current_card,
                         chapter_content=chapter_content,
                         total_cards=len(filtered_cards),
                         cards_json=json.dumps(filtered_cards),
                         contents_json=json.dumps(chapter_contents))

@app.route('/practice')
def practice_mode():
    """Practice quiz mode"""
    if not session.get('current_subject') or not session.get('subject_data'):
        return redirect(url_for('index'))
    
    # Get selected chapters from request args or use all chapters
    selected_chapters = request.args.getlist('chapters')
    if not selected_chapters:
        # If no chapters selected, use all available chapters
        selected_chapters = [c['id'] for c in session['subject_data']['chapters'] if not c['is_main_chapter']]
    
    # Filter practice questions by selected chapters
    all_questions = session['subject_data']['practice_questions']
    filtered_questions = [q for q in all_questions if q['chapter'] in selected_chapters]
    
    if not filtered_questions:
        return redirect(url_for('index'))
    
    # Shuffle questions for random order
    random.shuffle(filtered_questions)
    
    # Get current question (first one)
    current_question = filtered_questions[0]
    
    # Get chapter contents for the practice mode
    chapter_contents = session['subject_data']['chapter_contents']

    return render_template('practice.html',
                         current_question=current_question,
                         total_questions=len(filtered_questions),
                         questions_json=json.dumps(filtered_questions),
                         contents_json=json.dumps(chapter_contents))

@app.route('/api/chapters')
def api_chapters():
    """API endpoint to get chapters for current subject"""
    if not session.get('subject_data'):
        return jsonify([])
    
    return jsonify(session['subject_data']['chapters'])

@app.route('/api/learning_cards')
def api_learning_cards():
    """API endpoint to get learning cards for selected chapters"""
    if not session.get('subject_data'):
        return jsonify([])
    
    selected_chapters = request.args.getlist('chapters')
    all_cards = session['subject_data']['learning_cards']
    
    if selected_chapters:
        filtered_cards = [card for card in all_cards if card['chapter'] in selected_chapters]
    else:
        filtered_cards = all_cards
    
    random.shuffle(filtered_cards)
    return jsonify(filtered_cards)

@app.route('/api/practice_questions')
def api_practice_questions():
    """API endpoint to get practice questions for selected chapters"""
    if not session.get('subject_data'):
        return jsonify([])
    
    selected_chapters = request.args.getlist('chapters')
    all_questions = session['subject_data']['practice_questions']
    
    if selected_chapters:
        filtered_questions = [q for q in all_questions if q['chapter'] in selected_chapters]
    else:
        filtered_questions = all_questions
    
    random.shuffle(filtered_questions)
    return jsonify(filtered_questions)

@app.route('/api/chapter_content/<chapter_id>')
def api_chapter_content(chapter_id):
    """API endpoint to get content for a specific chapter"""
    if not session.get('subject_data'):
        return jsonify({})
    
    chapter_contents = session['subject_data']['chapter_contents']
    content = next((cc for cc in chapter_contents if cc['chapter'] == chapter_id), {})
    
    return jsonify(content)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
