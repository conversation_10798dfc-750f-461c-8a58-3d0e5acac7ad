#!/usr/bin/env python3
"""
Comprehensive test script for LearnFast application
Tests all major functions and endpoints
"""

import requests
import time

def test_learnfast():
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    print("🚀 Testing LearnFast Application")
    print("=" * 50)
    
    # Test 1: Main page
    print("1. Testing main page...")
    r = session.get(base_url)
    assert r.status_code == 200, f"Main page failed: {r.status_code}"
    print("   ✅ Main page loads successfully")
    
    # Test 2: Subject selection
    print("2. Testing subject selection...")
    r = session.post(f"{base_url}/select_subject", data={'subject': 'CT-AI'})
    assert r.status_code in [200, 302], f"Subject selection failed: {r.status_code}"
    print("   ✅ Subject selection works")
    
    # Test 3: Learning Cards
    print("3. Testing Learning Cards...")
    r = session.get(f"{base_url}/learning?chapters=1.1&types=Definition+Card")
    assert r.status_code == 200, f"Learning Cards failed: {r.status_code}"
    assert "Learning Cards" in r.text, "Learning Cards content missing"
    print("   ✅ Learning Cards mode works")
    
    # Test 4: Practice Quiz
    print("4. Testing Practice Quiz...")
    r = session.get(f"{base_url}/practice?chapters=1.1")
    assert r.status_code == 200, f"Practice Quiz failed: {r.status_code}"
    assert "Practice Quiz" in r.text, "Practice Quiz content missing"
    print("   ✅ Practice Quiz mode works")
    
    # Test 5: Reading Mode
    print("5. Testing Reading Mode...")
    r = session.get(f"{base_url}/reading?chapters=1.1&chapters=1.2")
    assert r.status_code == 200, f"Reading Mode failed: {r.status_code}"
    assert "Tananyag Olvasás" in r.text, "Reading Mode content missing"
    print("   ✅ Reading Mode works")
    
    # Test 6: Big Random
    print("6. Testing Big Random...")
    r = session.get(f"{base_url}/big-random")
    assert r.status_code == 200, f"Big Random failed: {r.status_code}"
    assert "Big Random" in r.text, "Big Random content missing"
    assert "Következő kérdés" in r.text, "Next button missing in Big Random"
    print("   ✅ Big Random mode works")
    
    # Test 7: API Endpoints
    print("7. Testing API endpoints...")
    
    # Chapters API
    r = session.get(f"{base_url}/api/chapters")
    assert r.status_code == 200, f"Chapters API failed: {r.status_code}"
    print("   ✅ Chapters API works")
    
    # Learning Cards API
    r = session.get(f"{base_url}/api/learning_cards?chapters=1.1")
    assert r.status_code == 200, f"Learning Cards API failed: {r.status_code}"
    print("   ✅ Learning Cards API works")
    
    # Practice Questions API
    r = session.get(f"{base_url}/api/practice_questions?chapters=1.1")
    assert r.status_code == 200, f"Practice Questions API failed: {r.status_code}"
    print("   ✅ Practice Questions API works")
    
    print("\n🎉 ALL TESTS PASSED!")
    print("=" * 50)
    print("✅ Main page")
    print("✅ Subject selection")
    print("✅ Learning Cards (with type filtering)")
    print("✅ Practice Quiz (with content display)")
    print("✅ Reading Mode (with tree navigation)")
    print("✅ Big Random (with dynamic UI)")
    print("✅ API endpoints")
    print("\n🏆 LearnFast is fully functional!")

if __name__ == "__main__":
    try:
        test_learnfast()
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        print("Make sure the Flask server is running on http://127.0.0.1:5000")
