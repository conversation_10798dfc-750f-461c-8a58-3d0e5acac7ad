# LearnFast - Executable Build Guide

## Áttekintés

Ez a dokumentum leírja, hogyan kell elkészíteni a LearnFast alkalmazás .exe fájlj<PERSON>t, amely konzol ablakkal rendelkezik és a `books` mappát a futtatási könyvtárból olvassa.

## Előfeltételek

- Python 3.8 vagy <PERSON>
- pip package manager
- Windows operációs rendszer

## Build folyamat

### 1. Automatikus build

A legegyszerűbb módja a build futtatásának:

```cmd
build.bat
```

Ez a script automatikusan:
- Ellenőrzi a Python telepítését
- Telepíti a PyInstaller-t (ha szükséges)
- Telepíti/frissíti a függőségeket
- Megtisztítja az előző build-eket
- Elkészíti az executable-t

### 2. <PERSON><PERSON><PERSON><PERSON> build

Ha manuálisan szeretnéd futtatni:

```cmd
# Függőségek telepítése
pip install -r requirements.txt
pip install pyinstaller

# Build futtatása
pyinstaller build.spec
```

## Build eredmény

A sikeres build után:
- Az executable a `dist\LearnFast.exe` helyen található
- A program konzol ablakkal indul
- Automatikusan megnyitja a böngészőt a http://localhost:5000 címen

## Fontos megjegyzések

### Books mappa kezelése

- A `books` mappa **NEM** épül be az executable-be
- A programnak a `books` mappát a futtatási könyvtárban kell megtalálnia
- A `LearnFast.exe` és a `books` mappa ugyanabban a könyvtárban kell legyenek

### Telepítési struktúra

```
LearnFast/
├── LearnFast.exe
└── books/
    ├── chapters.xlsx
    ├── learning.xlsx
    ├── practice.xlsx
    └── [subject folders]/
        └── DATA/
            └── [chapter files].txt
```

### Hibaelhárítás

1. **"books directory not found" hiba:**
   - Ellenőrizd, hogy a `books` mappa ugyanabban a könyvtárban van-e, mint a `LearnFast.exe`

2. **"Python not found" hiba a build során:**
   - Telepítsd a Python-t és add hozzá a PATH-hoz

3. **"PyInstaller failed" hiba:**
   - Futtasd újra a `build.bat` script-et
   - Ellenőrizd a függőségeket: `pip install -r requirements.txt`

## Fejlesztői megjegyzések

- A `main.py` fájl szolgál belépési pontként
- A `data_loader.py` módosítva lett, hogy a `books` mappát a futtatási könyvtárból keresse
- A `build.spec` fájl tartalmazza a PyInstaller konfigurációt
- A konzol ablak nyitva marad a hibakeresés és a státusz üzenetek miatt
