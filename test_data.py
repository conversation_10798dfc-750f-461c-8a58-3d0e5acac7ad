#!/usr/bin/env python3
"""Test script to verify data loading functionality"""

from data_loader import DataLoader

def test_data_loading():
    loader = DataLoader()
    
    print("Available subjects:")
    subjects = loader.get_available_subjects()
    print(subjects)
    
    if subjects:
        subject_name = subjects[0]
        print(f"\nLoading subject: {subject_name}")
        
        try:
            subject_data = loader.load_subject(subject_name)
            
            print(f"\nChapters ({len(subject_data.chapters)}):")
            for chapter in subject_data.chapters:
                print(f"  {chapter.id} - {chapter.name} (Main: {chapter.is_main_chapter})")
            
            print(f"\nLearning Cards ({len(subject_data.learning_cards)}):")
            for i, card in enumerate(subject_data.learning_cards[:3]):  # Show first 3
                print(f"  {i+1}. Chapter {card.chapter}: {card.question[:50]}...")
            
            print(f"\nPractice Questions ({len(subject_data.practice_questions)}):")
            for i, question in enumerate(subject_data.practice_questions[:3]):  # Show first 3
                print(f"  {i+1}. Chapter {question.chapter}: {question.question[:50]}...")
            
            print(f"\nChapter Contents ({len(subject_data.chapter_contents)}):")
            for content in subject_data.chapter_contents:
                print(f"  {content.chapter}: {content.content_en[:50]}...")
                
        except Exception as e:
            print(f"Error loading subject data: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_data_loading()
