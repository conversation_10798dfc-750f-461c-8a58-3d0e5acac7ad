#!/usr/bin/env python3
"""
LearnFast Application - Main Entry Point
This file serves as the main entry point for the executable version.
"""

import os
import sys
import webbrowser
import threading
import time
from app import app

def open_browser():
    """Open the default web browser to the application URL after a short delay"""
    time.sleep(1.5)  # Wait for Flask to start
    webbrowser.open('http://localhost:5000')

def main():
    """Main function to start the Flask application"""
    print("=" * 50)
    print("LearnFast Application Starting...")
    print("=" * 50)
    
    # Check if books directory exists in the current working directory
    books_path = os.path.join(os.getcwd(), "books")
    if not os.path.exists(books_path):
        print(f"ERROR: 'books' directory not found in current directory!")
        print(f"Expected path: {books_path}")
        print("Please make sure the 'books' folder is in the same directory as this executable.")
        input("Press Enter to exit...")
        sys.exit(1)
    
    print(f"Books directory found: {books_path}")
    print("Starting Flask server...")
    print("The application will open in your default web browser.")
    print("To stop the application, close this console window or press Ctrl+C")
    print("=" * 50)
    
    # Start browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # Start Flask application
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\nApplication stopped by user.")
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == '__main__':
    main()
