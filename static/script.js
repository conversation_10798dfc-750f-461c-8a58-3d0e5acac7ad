// Global JavaScript functions for LearnFast application

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any global functionality here
    console.log('LearnFast application loaded');
    
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add loading states for buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit' || this.tagName === 'A') {
                this.style.opacity = '0.7';
                this.style.pointerEvents = 'none';
                
                // Re-enable after 2 seconds (fallback)
                setTimeout(() => {
                    this.style.opacity = '1';
                    this.style.pointerEvents = 'auto';
                }, 2000);
            }
        });
    });
});

// Utility functions
function showElement(element) {
    if (element) {
        element.style.display = 'block';
    }
}

function hideElement(element) {
    if (element) {
        element.style.display = 'none';
    }
}

function toggleElement(element) {
    if (element) {
        element.style.display = element.style.display === 'none' ? 'block' : 'none';
    }
}

// Function to shuffle array (Fisher-Yates algorithm)
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// Function to get selected chapters from checkboxes
function getSelectedChapters() {
    const checkboxes = document.querySelectorAll('input[name="chapters"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// Function to store selected chapters in sessionStorage
function storeSelectedChapters() {
    const selected = getSelectedChapters();
    sessionStorage.setItem('selectedChapters', JSON.stringify(selected));
}

// Function to load selected chapters from sessionStorage
function loadSelectedChapters() {
    const stored = sessionStorage.getItem('selectedChapters');
    if (stored) {
        const chapters = JSON.parse(stored);
        chapters.forEach(chapterId => {
            const checkbox = document.querySelector(`input[name="chapters"][value="${chapterId}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }
}

// Auto-save chapter selection
document.addEventListener('change', function(e) {
    if (e.target.name === 'chapters') {
        storeSelectedChapters();
    }
});

// Load saved chapters on page load
window.addEventListener('load', function() {
    loadSelectedChapters();
});
