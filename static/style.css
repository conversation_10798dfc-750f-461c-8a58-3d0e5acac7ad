/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* Header and Navigation */
header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-container h1 a {
    color: white;
    text-decoration: none;
    font-size: 1.8rem;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.current-subject {
    background-color: #34495e;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-size: 0.9rem;
}

/* Main content */
main {
    min-height: calc(100vh - 120px);
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    margin: 0.25rem;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #229954;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

/* Grid layouts */
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.subject-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
}

.subject-card h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* Study options */
.study-options {
    margin-top: 3rem;
}

.mode-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.mode-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
}

.mode-card h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.mode-card p {
    margin-bottom: 1.5rem;
    color: #666;
}

/* Chapter selection */
.chapter-selection, .learning-types-selection {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.chapters-grid, .types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.chapter-item, .type-item {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.chapter-item label, .type-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.chapter-item input[type="checkbox"], .type-item input[type="checkbox"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

.chapter-label, .type-label {
    flex: 1;
}

.main-chapter {
    accent-color: #e74c3c;
}

.selection-info {
    color: #666;
    margin-bottom: 1rem;
    font-style: italic;
}

.form-actions {
    margin-top: 1rem;
    text-align: center;
}

/* Learning and Practice cards */
.learning-header, .practice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #3498db;
}

.progress-info {
    font-size: 1.2rem;
    font-weight: bold;
    color: #3498db;
}

.learning-card, .practice-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.card-content {
    margin-bottom: 2rem;
}

.question-section, .answer-section, .chapter-content-section, .options-section, .solution-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.question-section h3, .answer-section h3, .chapter-content-section h3, .options-section h3, .solution-section h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.question-text, .answer-text, .content-text {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 5px;
    border-left: 4px solid #3498db;
    white-space: pre-line; /* Preserve line breaks but allow HTML */
    word-wrap: break-word; /* Break long words if needed */
}

/* Content title styling */
.content-text .content-title,
.content-text-hu .content-title {
    font-weight: bold;
    font-size: 1.2rem;
    color: #2c3e50;
    display: block;
    margin-bottom: 0.5rem;
}

.question-text-hu, .answer-text-hu, .content-text-hu {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #e8f4fd;
    border-radius: 5px;
    border-left: 4px solid #17a2b8;
    white-space: pre-line; /* Preserve line breaks but allow HTML */
    word-wrap: break-word; /* Break long words if needed */
}

/* Practice specific styles */
.options-grid {
    display: grid;
    gap: 1rem;
    margin: 1rem 0;
}

.option-item {
    background: white;
    padding: 1rem;
    border-radius: 5px;
    border: 2px solid #ddd;
    transition: all 0.3s ease;
}

.option-item:hover {
    border-color: #3498db;
    background-color: #f8f9fa;
}

.option-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
}

.option-item input[type="radio"] {
    margin-right: 1rem;
    transform: scale(1.3);
}

.option-text {
    flex: 1;
}

.option-en, .option-hu {
    display: block;
    font-size: 1rem;
    line-height: 1.4;
}

.option-hu {
    color: #666;
    font-style: italic;
    margin-top: 0.5rem;
}

.correct-answer, .correct-answer-hu {
    font-size: 1.1rem;
    font-weight: bold;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.correct-answer {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.correct-answer-hu {
    background-color: #cce7ff;
    color: #004085;
    border-left: 4px solid #007bff;
}

.feedback {
    padding: 1rem;
    border-radius: 5px;
    font-weight: bold;
    margin-top: 1rem;
}

.feedback.correct {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.feedback.incorrect {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Action buttons */
.card-actions, .practice-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

.learning-controls, .practice-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: auto;
}

/* Reading mode styles */
.reading-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    gap: 2rem;
    min-height: calc(100vh - 120px);
}

.reading-sidebar {
    width: 300px;
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.reading-main {
    flex: 1;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.reading-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #3498db;
}

.chapter-counter {
    color: #666;
    font-size: 0.9rem;
}

.chapter-tree {
    max-height: 70vh;
    overflow-y: auto;
}

.chapter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.main-chapter-item {
    margin-bottom: 1rem;
}

.main-chapter-title {
    font-weight: bold;
    color: #2c3e50;
    display: block;
    padding: 0.5rem;
    background-color: #ecf0f1;
    border-radius: 5px;
}

.sub-chapter-list {
    list-style: none;
    padding-left: 1rem;
    margin-top: 0.5rem;
}

.sub-chapter-item {
    margin-bottom: 0.25rem;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.sub-chapter-item:hover {
    background-color: #f8f9fa;
}

.sub-chapter-item.active {
    background-color: #3498db;
    color: white;
}

.sub-chapter-title {
    display: block;
    padding: 0.5rem;
    font-size: 0.9rem;
}

.sub-chapter-title.has-content {
    color: #27ae60;
    font-weight: 500;
}

.sub-chapter-title.no-content {
    color: #95a5a6;
    font-style: italic;
}

.sub-chapter-item.active .sub-chapter-title {
    color: white;
}

.reading-content {
    margin-bottom: 2rem;
}

.content-section h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.content-actions {
    margin-top: 1.5rem;
    text-align: center;
}

.reading-navigation {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

.reading-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.no-content {
    text-align: center;
    padding: 3rem;
    color: #666;
}

/* Big Random styles */
.big-random-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e74c3c;
}

.question-type {
    background-color: #e74c3c;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 1rem;
}

.big-random-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.learning-type {
    border-left: 4px solid #27ae60;
}

.practice-type {
    border-left: 4px solid #f39c12;
}

.big-random-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.no-questions {
    text-align: center;
    padding: 3rem;
    color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-links {
        flex-direction: column;
        text-align: center;
    }

    .container {
        padding: 0 1rem;
    }

    .subjects-grid, .mode-selection, .chapters-grid {
        grid-template-columns: 1fr;
    }

    .learning-header, .practice-header, .reading-header, .big-random-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .card-actions, .practice-actions, .learning-controls, .practice-controls, .reading-controls, .big-random-controls {
        flex-direction: column;
        align-items: center;
    }

    .reading-container {
        flex-direction: column;
        padding: 0 1rem;
    }

    .reading-sidebar {
        width: 100%;
        position: static;
        order: 2;
    }

    .reading-main {
        order: 1;
    }

    .reading-navigation {
        flex-direction: column;
        gap: 1rem;
    }
}
